# Test configuration for template security
spring:
  cloud:
    config:
      enabled: false
      import-check:
        enabled: false

template:
  security:
    # Test with smaller limits for faster testing
    max-size: 10000
    max-nesting-depth: 5
    processing-timeout: 2000
    max-output-size: 50000
    strict-validation: true
    log-security-violations: true

# Test logging configuration
logging:
  level:
    com.dell.it.eis.email.security: DEBUG
    root: INFO
