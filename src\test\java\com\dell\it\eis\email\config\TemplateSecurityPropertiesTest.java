package com.dell.it.eis.email.config;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(properties = {
    "spring.cloud.config.enabled=false",
    "spring.cloud.config.import-check.enabled=false"
})
class TemplateSecurityPropertiesTest {

    @Autowired
    private TemplateSecurityProperties securityProperties;

    @Test
    void testDefaultProperties() {
        assertNotNull(securityProperties);
        assertEquals(50000, securityProperties.getMaxSize());
        assertEquals(10, securityProperties.getMaxNestingDepth());
        assertEquals(5000L, securityProperties.getProcessingTimeout());
        assertEquals(500000, securityProperties.getMaxOutputSize());
        assertTrue(securityProperties.getStrictValidation());
        assertTrue(securityProperties.getLogSecurityViolations());
    }

    @Test
    void testSafeBounds() {
        // Test that safe methods return values within bounds
        assertTrue(securityProperties.getMaxSizeSafe() >= 1000);
        assertTrue(securityProperties.getMaxSizeSafe() <= 1000000);
        
        assertTrue(securityProperties.getMaxNestingDepthSafe() >= 1);
        assertTrue(securityProperties.getMaxNestingDepthSafe() <= 50);
        
        assertTrue(securityProperties.getProcessingTimeoutSafe() >= 1000L);
        assertTrue(securityProperties.getProcessingTimeoutSafe() <= 60000L);
        
        assertTrue(securityProperties.getMaxOutputSizeSafe() >= 10000);
        assertTrue(securityProperties.getMaxOutputSizeSafe() <= 10000000);
    }

    @Test
    void testValidateConfiguration() {
        // Should not throw exception with default values
        assertDoesNotThrow(() -> securityProperties.validateConfiguration());
    }

    @SpringBootTest(properties = {
        "spring.cloud.config.enabled=false",
        "spring.cloud.config.import-check.enabled=false",
        "template.security.max-size=25000",
        "template.security.max-nesting-depth=5",
        "template.security.processing-timeout=3000",
        "template.security.max-output-size=250000",
        "template.security.strict-validation=false",
        "template.security.log-security-violations=false"
    })
    static class CustomPropertiesTest {

        @Autowired
        private TemplateSecurityProperties securityProperties;

        @Test
        void testCustomProperties() {
            assertEquals(25000, securityProperties.getMaxSize());
            assertEquals(5, securityProperties.getMaxNestingDepth());
            assertEquals(3000L, securityProperties.getProcessingTimeout());
            assertEquals(250000, securityProperties.getMaxOutputSize());
            assertFalse(securityProperties.getStrictValidation());
            assertFalse(securityProperties.getLogSecurityViolations());
        }
    }

    @SpringBootTest(properties = {
        "spring.cloud.config.enabled=false",
        "spring.cloud.config.import-check.enabled=false",
        "template.security.max-size=500",  // Below minimum
        "template.security.max-nesting-depth=0",  // Below minimum
        "template.security.processing-timeout=500",  // Below minimum
        "template.security.max-output-size=5000"  // Below minimum
    })
    static class BoundaryPropertiesTest {

        @Autowired
        private TemplateSecurityProperties securityProperties;

        @Test
        void testBoundaryValues() {
            // Safe methods should enforce minimum bounds
            assertEquals(1000, securityProperties.getMaxSizeSafe());
            assertEquals(1, securityProperties.getMaxNestingDepthSafe());
            assertEquals(1000L, securityProperties.getProcessingTimeoutSafe());
            assertEquals(10000, securityProperties.getMaxOutputSizeSafe());
        }
    }

    @SpringBootTest(properties = {
        "spring.cloud.config.enabled=false",
        "spring.cloud.config.import-check.enabled=false",
        "template.security.max-size=2000000",  // Above maximum
        "template.security.max-nesting-depth=100",  // Above maximum
        "template.security.processing-timeout=120000",  // Above maximum
        "template.security.max-output-size=20000000"  // Above maximum
    })
    static class UpperBoundaryPropertiesTest {

        @Autowired
        private TemplateSecurityProperties securityProperties;

        @Test
        void testUpperBoundaryValues() {
            // Safe methods should enforce maximum bounds
            assertEquals(1000000, securityProperties.getMaxSizeSafe());
            assertEquals(50, securityProperties.getMaxNestingDepthSafe());
            assertEquals(60000L, securityProperties.getProcessingTimeoutSafe());
            assertEquals(10000000, securityProperties.getMaxOutputSizeSafe());
        }
    }
}
