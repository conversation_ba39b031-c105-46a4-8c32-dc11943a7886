package com.dell.it.eis.email.security;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.dell.it.eis.email.config.TemplateSecurityProperties;
import com.dell.it.eis.email.exception.TemplateSecurityException;

/**
 * Integration tests for template security with configurable properties
 */
@SpringBootTest(
    properties = {
        "spring.cloud.config.enabled=false",
        "spring.cloud.config.import-check.enabled=false",
        "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration",
        "redis.service.url=http://localhost:8080",
        "email.smtp.host=localhost",
        "email.smtp.username=test",
        "email.smtp.password=test",
        "email.smtp.port=587",
        "api.user.name=test",
        "api.user.password=test",
        "mail.service.core.pool.size=2",
        "mail.service.max.pool.size=4",
        "mail.service.queue.capacity=10"
    },
    webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class TemplateSecurityIntegrationTest {

    @Autowired
    private TemplateSecurityValidator validator;

    @Autowired
    private SecureTemplateService secureTemplateService;

    @Autowired
    private TemplateSecurityProperties securityProperties;

    @Test
    void testSecuritySystemWithDefaultConfiguration() {
        // Test that the security system works with default configuration
        String safeTemplate = "Hello $name, your order #$orderNumber is ready!";
        Map<String, Object> model = new HashMap<>();
        model.put("name", "John Doe");
        model.put("orderNumber", "12345");

        // Validation should pass
        assertDoesNotThrow(() -> validator.validateTemplate(safeTemplate));

        // Template processing should work
        String result = secureTemplateService.processTemplateSecurely(safeTemplate, model);
        assertNotNull(result);
        assertTrue(result.contains("John Doe"));
        assertTrue(result.contains("12345"));
    }

    @Test
    void testSecuritySystemBlocksMaliciousTemplate() {
        String maliciousTemplate = "Hello ${Runtime.getRuntime()}";
        Map<String, Object> model = new HashMap<>();

        // Validation should fail
        TemplateSecurityException exception = assertThrows(
            TemplateSecurityException.class,
            () -> validator.validateTemplate(maliciousTemplate)
        );
        assertTrue(exception.getMessage().contains("dangerous content"));

        // Template processing should also fail
        TemplateSecurityException serviceException = assertThrows(
            TemplateSecurityException.class,
            () -> secureTemplateService.processTemplateSecurely(maliciousTemplate, model)
        );
        assertTrue(serviceException.getMessage().contains("dangerous content"));
    }

    @SpringBootTest(
        properties = {
            "spring.cloud.config.enabled=false",
            "spring.cloud.config.import-check.enabled=false",
            "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration",
            "redis.service.url=http://localhost:8080",
            "email.smtp.host=localhost",
            "email.smtp.username=test",
            "email.smtp.password=test",
            "email.smtp.port=587",
            "api.user.name=test",
            "api.user.password=test",
            "mail.service.core.pool.size=2",
            "mail.service.max.pool.size=4",
            "mail.service.queue.capacity=10",
            "template.security.max-size=1000",
            "template.security.max-nesting-depth=3",
            "template.security.processing-timeout=2000",
            "template.security.max-output-size=5000",
            "template.security.strict-validation=true",
            "template.security.log-security-violations=true"
        },
        webEnvironment = SpringBootTest.WebEnvironment.NONE
    )
    static class CustomConfigurationTest {

        @Autowired
        private TemplateSecurityValidator validator;

        @Autowired
        private SecureTemplateService secureTemplateService;

        @Autowired
        private TemplateSecurityProperties securityProperties;

        @Test
        void testCustomSizeLimit() {
            // Verify custom configuration is loaded
            assertEquals(1000, securityProperties.getMaxSize());

            // Create template larger than custom limit
            StringBuilder largeTemplate = new StringBuilder();
            for (int i = 0; i < 1500; i++) {
                largeTemplate.append("a");
            }

            // Should fail with custom limit
            TemplateSecurityException exception = assertThrows(
                TemplateSecurityException.class,
                () -> validator.validateTemplate(largeTemplate.toString())
            );
            assertTrue(exception.getMessage().contains("1000 characters"));
        }

        @Test
        void testCustomNestingDepthLimit() {
            // Verify custom configuration is loaded
            assertEquals(3, securityProperties.getMaxNestingDepth());

            // Create template with nesting deeper than custom limit
            StringBuilder nestedTemplate = new StringBuilder();
            for (int i = 0; i < 5; i++) {
                nestedTemplate.append("#if($condition").append(i).append(")\n");
            }
            nestedTemplate.append("Hello World\n");
            for (int i = 0; i < 5; i++) {
                nestedTemplate.append("#end\n");
            }

            // Should fail with custom limit
            TemplateSecurityException exception = assertThrows(
                TemplateSecurityException.class,
                () -> validator.validateTemplate(nestedTemplate.toString())
            );
            assertTrue(exception.getMessage().contains("nesting depth exceeds"));
        }

        @Test
        void testCustomOutputSizeLimit() {
            // Verify custom configuration is loaded
            assertEquals(5000, securityProperties.getMaxOutputSize());

            String template = "Hello $name";
            Map<String, Object> model = new HashMap<>();
            
            // Create a large name that would result in output > 5000 characters
            StringBuilder largeName = new StringBuilder();
            for (int i = 0; i < 6000; i++) {
                largeName.append("a");
            }
            model.put("name", largeName.toString());

            // Should fail due to output size limit
            TemplateSecurityException exception = assertThrows(
                TemplateSecurityException.class,
                () -> secureTemplateService.processTemplateSecurely(template, model)
            );
            assertTrue(exception.getMessage().contains("output exceeds maximum size"));
        }
    }

    @SpringBootTest(
        properties = {
            "spring.cloud.config.enabled=false",
            "spring.cloud.config.import-check.enabled=false",
            "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration",
            "redis.service.url=http://localhost:8080",
            "email.smtp.host=localhost",
            "email.smtp.username=test",
            "email.smtp.password=test",
            "email.smtp.port=587",
            "api.user.name=test",
            "api.user.password=test",
            "mail.service.core.pool.size=2",
            "mail.service.max.pool.size=4",
            "mail.service.queue.capacity=10",
            "template.security.strict-validation=false",
            "template.security.log-security-violations=false"
        },
        webEnvironment = SpringBootTest.WebEnvironment.NONE
    )
    static class NonStrictModeTest {

        @Autowired
        private TemplateSecurityValidator validator;

        @Autowired
        private TemplateSecurityProperties securityProperties;

        @Test
        void testNonStrictMode() {
            // Verify non-strict mode is configured
            assertFalse(securityProperties.getStrictValidation());
            assertFalse(securityProperties.getLogSecurityViolations());

            // Malicious template should not throw exception in non-strict mode
            String maliciousTemplate = "Hello ${Runtime.getRuntime()}";
            assertDoesNotThrow(() -> validator.validateTemplate(maliciousTemplate));
        }
    }

    @Test
    void testConfigurationValidation() {
        // Test that configuration validation works
        assertDoesNotThrow(() -> securityProperties.validateConfiguration());
        
        // Verify configuration values are within expected ranges
        assertTrue(securityProperties.getMaxSizeSafe() >= 1000);
        assertTrue(securityProperties.getMaxNestingDepthSafe() >= 1);
        assertTrue(securityProperties.getProcessingTimeoutSafe() >= 1000L);
        assertTrue(securityProperties.getMaxOutputSizeSafe() >= 10000);
    }
}
