package com.dell.it.eis.email.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * Configuration properties for template security settings
 */
@Component
@ConfigurationProperties(prefix = "template.security")
@Data
@Validated
@Slf4j
public class TemplateSecurityProperties {

    /**
     * Maximum template size in characters (default: 50KB)
     */
    @NotNull
    @Min(value = 1000, message = "Template max size must be at least 1000 characters")
    private Integer maxSize = 50000;

    /**
     * Maximum nesting depth for Velocity constructs (default: 10)
     */
    @NotNull
    @Min(value = 1, message = "Template max nesting depth must be at least 1")
    private Integer maxNestingDepth = 10;

    /**
     * Processing timeout in milliseconds (default: 5 seconds)
     */
    @NotNull
    @Min(value = 1000, message = "Processing timeout must be at least 1000ms")
    private Long processingTimeout = 5000L;

    /**
     * Maximum output size in characters (default: 500KB)
     */
    @NotNull
    @Min(value = 10000, message = "Max output size must be at least 10000 characters")
    private Integer maxOutputSize = 500000;

    /**
     * Whether to enable strict validation mode (default: true)
     */
    private Boolean strictValidation = true;

    /**
     * Whether to log security violations (default: true)
     */
    private Boolean logSecurityViolations = true;

    /**
     * Validates the configuration properties after binding
     */
    public void validateConfiguration() {
        log.info("Template Security Configuration:");
        log.info("  Max Template Size: {} characters", maxSize);
        log.info("  Max Nesting Depth: {}", maxNestingDepth);
        log.info("  Processing Timeout: {}ms", processingTimeout);
        log.info("  Max Output Size: {} characters", maxOutputSize);
        log.info("  Strict Validation: {}", strictValidation);
        log.info("  Log Security Violations: {}", logSecurityViolations);

        // Additional validation logic
        if (maxOutputSize <= maxSize) {
            log.warn("Max output size ({}) should be larger than max template size ({})", 
                    maxOutputSize, maxSize);
        }

        if (processingTimeout < 1000) {
            log.warn("Processing timeout ({}ms) is very low and may cause legitimate templates to fail", 
                    processingTimeout);
        }

        if (maxNestingDepth > 20) {
            log.warn("Max nesting depth ({}) is very high and may allow complex attacks", 
                    maxNestingDepth);
        }
    }

    /**
     * Gets the maximum template size with bounds checking
     */
    public int getMaxSizeSafe() {
        return Math.max(1000, Math.min(1000000, maxSize)); // Between 1KB and 1MB
    }

    /**
     * Gets the maximum nesting depth with bounds checking
     */
    public int getMaxNestingDepthSafe() {
        return Math.max(1, Math.min(50, maxNestingDepth)); // Between 1 and 50
    }

    /**
     * Gets the processing timeout with bounds checking
     */
    public long getProcessingTimeoutSafe() {
        return Math.max(1000L, Math.min(60000L, processingTimeout)); // Between 1s and 60s
    }

    /**
     * Gets the maximum output size with bounds checking
     */
    public int getMaxOutputSizeSafe() {
        return Math.max(10000, Math.min(10000000, maxOutputSize)); // Between 10KB and 10MB
    }
}
